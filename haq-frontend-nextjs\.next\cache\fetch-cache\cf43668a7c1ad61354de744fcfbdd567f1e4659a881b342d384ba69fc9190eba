{"kind": "FETCH", "data": {"headers": {"cache-control": "public, max-age=300, s-maxage=300", "connection": "keep-alive", "content-type": "application/json", "date": "Mon, 23 Jun 2025 20:16:24 GMT", "keep-alive": "timeout=5", "transfer-encoding": "chunked", "vary": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJzdWNjZXNzIjp0cnVlLCJkYXRhIjp7ImNvbXBhbnkiOnsiY29tcGFueV9pZCI6IjljMDlkZWQwLWYxYWEtNGU5NS05ZTkwLTFkNjUxZGNlOWNkZCIsIm5hbWUiOiJEYXRhVmlzaW9uIEFuYWx5dGljcyIsInNsdWciOiJkYXRhdmlzaW9uLWFuYWx5dGljcyIsImluZHVzdHJ5IjoiVGVjaG5vbG9neSIsImxvY2F0aW9uIjoiS2FyYWNoaSIsIndlYnNpdGVfdXJsIjoiaHR0cHM6Ly9kYXRhdmlzaW9uLnBrIiwibG9nb191cmwiOiJodHRwczovL2ltYWdlcy5wZXhlbHMuY29tL3Bob3Rvcy8zMTg0NDY1L3BleGVscy1waG90by0zMTg0NDY1LmpwZWc/YXV0bz1jb21wcmVzcyZjcz10aW55c3JnYiZ3PTEwMCIsImVtcGxveWVlX2NvdW50X3JhbmdlIjoiMTAtNTAiLCJmb3VuZGVkX3llYXIiOjIwMjEsImRlc2NyaXB0aW9uIjoiRGF0YSBhbmFseXRpY3MgYW5kIGJ1c2luZXNzIGludGVsbGlnZW5jZSBzb2x1dGlvbnMiLCJoYXFfc2NvcmUiOjQuMSwidG90YWxfcmV2aWV3cyI6MCwiaXNfdmVyaWZpZWQiOmZhbHNlLCJjcmVhdGVkX2F0IjoiMjAyNS0wNi0yMlQwMjozOTo1NC43ODI1NzgrMDA6MDAiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNi0yMlQwMjozOTo1NC43ODI1NzgrMDA6MDAiLCJhdmVyYWdlX3JhdGluZyI6MCwicmF0aW5nX2Rpc3RyaWJ1dGlvbiI6eyIxIjowLCIyIjowLCIzIjowLCI0IjowLCI1IjowfX19fQ==", "status": 200, "url": "http://localhost:3000/api/companies/9c09ded0-f1aa-4e95-9e90-1d651dce9cdd"}, "revalidate": 300, "tags": []}