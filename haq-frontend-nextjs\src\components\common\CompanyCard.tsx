import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Star, MapPin, Users, AlertTriangle, CheckCircle } from 'lucide-react';

interface CompanyCardProps {
  company: {
    company_id?: string; // New UUID format
    id?: number; // Legacy support
    name: string;
    logo_url?: string;
    haq_score: number;
    total_reviews?: number;
    industry?: string;
    location?: string;
    redFlags?: string[];
    greenFlags?: string[];
  };
  delay?: number;
}

export const CompanyCard: React.FC<CompanyCardProps> = ({ company, delay = 0 }) => {
  const getScoreColor = (score: number) => {
    if (score >= 4.0) return 'text-green-400';
    if (score >= 3.0) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 4.0) return 'bg-green-400/20';
    if (score >= 3.0) return 'bg-yellow-400/20';
    return 'bg-red-400/20';
  };

  // Use company_id if available, fallback to legacy id
  const companyId = company.company_id || company.id;
  const companyUrl = `/companies/${companyId}`;

  return (
    <Link href={companyUrl}>
      <div 
        className="bg-surface-primary border border-border-primary rounded-medium p-6 hover:border-accent-primary transition-all duration-200 animate-slide-up group cursor-pointer"
        style={{ animationDelay: `${delay}ms` }}
      >
        {/* Header */}
        <div className="flex items-start space-x-4 mb-4">
          <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-surface-secondary flex-shrink-0">
            <Image
              src={company.logo_url || "/placeholder-company.svg"}
              alt={`${company.name} logo`}
              fill
              className="object-cover"
              sizes="48px"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-text-primary group-hover:text-accent-primary transition-colors duration-200 truncate">
              {company.name}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-text-secondary">
              {company.industry && <span>{company.industry}</span>}
              {company.location && (
                <div className="flex items-center space-x-1">
                  <MapPin className="w-3 h-3" />
                  <span>{company.location}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Haq Score */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className={`px-3 py-1 rounded-lg ${getScoreBgColor(company.haq_score || 0)}`}>
              <span className={`font-bold ${getScoreColor(company.haq_score || 0)}`}>
                {(company.haq_score || 0).toFixed(1)}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Star className={`w-4 h-4 ${getScoreColor(company.haq_score || 0)}`} fill="currentColor" />
              <span className="text-text-secondary text-sm">Haq Score</span>
            </div>
          </div>
          <div className="flex items-center space-x-1 text-text-secondary text-sm">
            <Users className="w-4 h-4" />
            <span>{company.total_reviews || 0} reviews</span>
          </div>
        </div>

        {/* Flags */}
        <div className="space-y-3">
          {/* Green Flags */}
          {company.greenFlags && company.greenFlags.length > 0 && (
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-sm font-medium text-text-primary">Positives</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {company.greenFlags.slice(0, 2).map((flag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-green-400/20 text-green-400 text-xs rounded-lg"
                  >
                    {flag}
                  </span>
                ))}
                {company.greenFlags.length > 2 && (
                  <span className="px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg">
                    +{company.greenFlags.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Red Flags */}
          {company.redFlags && company.redFlags.length > 0 && (
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-4 h-4 text-red-400" />
                <span className="text-sm font-medium text-text-primary">Issues</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {company.redFlags.slice(0, 2).map((flag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-red-400/20 text-red-400 text-xs rounded-lg"
                  >
                    {flag}
                  </span>
                ))}
                {company.redFlags.length > 2 && (
                  <span className="px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg">
                    +{company.redFlags.length - 2} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* View Details */}
        <div className="mt-4 pt-4 border-t border-border-primary">
          <span className="text-accent-primary text-sm font-medium group-hover:underline">
            View Details →
          </span>
        </div>
      </div>
    </Link>
  );
};
