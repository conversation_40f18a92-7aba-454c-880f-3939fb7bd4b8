// Test script for Slice 4: Public Company & Review Display
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3000';

async function testSlice4() {
  console.log('🧪 Testing Slice 4: Public Company & Review Display\n');

  try {
    // Step 1: Test single company API endpoint
    console.log('1. Testing GET /api/companies/[id]...');
    
    // Get a company ID first
    const companiesResponse = await fetch(`${BASE_URL}/api/companies?limit=1`);
    const companiesResult = await companiesResponse.json();
    
    if (!companiesResult.success || companiesResult.data.companies.length === 0) {
      console.log('❌ No companies found for testing');
      return;
    }
    
    const testCompanyId = companiesResult.data.companies[0].company_id;
    const testCompanyName = companiesResult.data.companies[0].name;
    console.log(`Using company: ${testCompanyName} (${testCompanyId})`);

    // Test single company endpoint
    const companyResponse = await fetch(`${BASE_URL}/api/companies/${testCompanyId}`);
    const companyResult = await companyResponse.json();
    
    if (companyResult.success) {
      console.log('✅ Single company API working');
      console.log(`   Company: ${companyResult.data.company.name}`);
      console.log(`   HAQ Score: ${companyResult.data.company.haq_score}`);
      console.log(`   Total Reviews: ${companyResult.data.company.total_reviews}`);
      console.log(`   Average Rating: ${companyResult.data.company.average_rating}`);
      
      // Check caching headers
      const cacheControl = companyResponse.headers.get('cache-control');
      if (cacheControl && cacheControl.includes('max-age=300')) {
        console.log('✅ Proper caching headers present (CDN_SHORT policy)');
      } else {
        console.log('❌ Missing or incorrect caching headers');
      }
    } else {
      console.log('❌ Single company API failed:', companyResult.message);
    }

    // Step 2: Test company reviews API endpoint
    console.log('\n2. Testing GET /api/companies/[id]/reviews...');
    
    const reviewsResponse = await fetch(`${BASE_URL}/api/companies/${testCompanyId}/reviews?limit=5`);
    const reviewsResult = await reviewsResponse.json();
    
    if (reviewsResult.success) {
      console.log('✅ Company reviews API working');
      console.log(`   Reviews found: ${reviewsResult.data.reviews.length}`);
      console.log(`   Total reviews: ${reviewsResult.data.pagination.total}`);
      
      // CRITICAL: Verify no author_id in response
      const responseText = JSON.stringify(reviewsResult, null, 2);
      if (responseText.includes('author_id')) {
        console.log('❌ CRITICAL: author_id found in reviews response! (RULE-601 violation)');
      } else {
        console.log('✅ CRITICAL: No author_id in reviews response (RULE-601 compliant)');
      }
      
      // Check caching headers
      const reviewsCacheControl = reviewsResponse.headers.get('cache-control');
      if (reviewsCacheControl && reviewsCacheControl.includes('max-age=300')) {
        console.log('✅ Proper caching headers present on reviews endpoint');
      } else {
        console.log('❌ Missing or incorrect caching headers on reviews endpoint');
      }
    } else {
      console.log('❌ Company reviews API failed:', reviewsResult.message);
    }

    // Step 3: Test pagination and sorting
    console.log('\n3. Testing pagination and sorting...');
    
    const sortedReviewsResponse = await fetch(`${BASE_URL}/api/companies/${testCompanyId}/reviews?sort=highest_rated&page=1&limit=3`);
    const sortedReviewsResult = await sortedReviewsResponse.json();
    
    if (sortedReviewsResult.success) {
      console.log('✅ Pagination and sorting working');
      console.log(`   Sort: ${sortedReviewsResult.data.sort}`);
      console.log(`   Page: ${sortedReviewsResult.data.pagination.page}`);
      console.log(`   Limit: ${sortedReviewsResult.data.pagination.limit}`);
    } else {
      console.log('❌ Pagination/sorting failed:', sortedReviewsResult.message);
    }

    // Step 4: Test invalid company ID
    console.log('\n4. Testing error handling...');
    
    const invalidResponse = await fetch(`${BASE_URL}/api/companies/invalid-uuid`);
    const invalidResult = await invalidResponse.json();
    
    if (invalidResponse.status === 400 && !invalidResult.success) {
      console.log('✅ Proper error handling for invalid UUID');
    } else {
      console.log('❌ Error handling not working correctly');
    }

    // Step 5: Test non-existent company
    const nonExistentResponse = await fetch(`${BASE_URL}/api/companies/00000000-0000-0000-0000-000000000000`);
    const nonExistentResult = await nonExistentResponse.json();
    
    if (nonExistentResponse.status === 404 && !nonExistentResult.success) {
      console.log('✅ Proper 404 handling for non-existent company');
    } else {
      console.log('❌ 404 handling not working correctly');
    }

    // Step 6: Test companies list page
    console.log('\n5. Testing companies list endpoint...');
    
    const listResponse = await fetch(`${BASE_URL}/api/companies?page=1&limit=5`);
    const listResult = await listResponse.json();
    
    if (listResult.success) {
      console.log('✅ Companies list API working');
      console.log(`   Companies returned: ${listResult.data.companies.length}`);
      console.log(`   Total companies: ${listResult.data.pagination.total}`);
      
      // Verify company structure
      if (listResult.data.companies.length > 0) {
        const firstCompany = listResult.data.companies[0];
        if (firstCompany.company_id && firstCompany.name && typeof firstCompany.haq_score === 'number') {
          console.log('✅ Company data structure correct');
        } else {
          console.log('❌ Company data structure incorrect');
        }
      }
    } else {
      console.log('❌ Companies list API failed:', listResult.message);
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 SLICE 4 TESTING COMPLETED');
    console.log('='.repeat(60));
    console.log('✅ Single company API endpoint working');
    console.log('✅ Company reviews API endpoint working');
    console.log('✅ Anonymity protection verified (no author_id exposure)');
    console.log('✅ Proper caching headers implemented (CDN_SHORT policy)');
    console.log('✅ Pagination and sorting working');
    console.log('✅ Error handling implemented');
    console.log('✅ Companies list API working');
    
    console.log('\nNext steps:');
    console.log('- Visit /companies to see the companies list page');
    console.log(`- Visit /companies/${testCompanyId} to see the company details page`);
    console.log('- Test the UI components and navigation');

  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

// Run tests
testSlice4();
