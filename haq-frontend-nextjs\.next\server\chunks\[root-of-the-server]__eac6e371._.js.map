{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\n// Environment variables (only accessed on server-side)\nconst getJWTSecret = () => {\n  const secret = process.env.JWT_SECRET;\n  if (!secret) {\n    throw new Error('JWT_SECRET environment variable is required');\n  }\n  return secret;\n};\n\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\n// Types\nexport interface User {\n  user_id: string;\n  username: string;\n  email: string;\n  role: 'user' | 'admin';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface JWTPayload {\n  user_id: string;\n  role: 'user' | 'admin';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  token?: string;\n  error?: string;\n}\n\n// Password hashing utilities\nexport class PasswordUtils {\n  private static readonly SALT_ROUNDS = 12; // High security salt rounds\n\n  /**\n   * Hash a password using bcrypt with salt\n   * @param password - Plain text password\n   * @returns Promise<string> - Hashed password\n   */\n  static async hashPassword(password: string): Promise<string> {\n    try {\n      const salt = await bcrypt.genSalt(this.SALT_ROUNDS);\n      const hash = await bcrypt.hash(password, salt);\n      return hash;\n    } catch (error) {\n      throw new Error('Failed to hash password');\n    }\n  }\n\n  /**\n   * Verify a password against its hash\n   * @param password - Plain text password\n   * @param hash - Stored password hash\n   * @returns Promise<boolean> - True if password matches\n   */\n  static async verifyPassword(password: string, hash: string): Promise<boolean> {\n    try {\n      return await bcrypt.compare(password, hash);\n    } catch (error) {\n      throw new Error('Failed to verify password');\n    }\n  }\n}\n\n// JWT utilities\nexport class JWTUtils {\n  /**\n   * Generate a JWT token for a user\n   * @param payload - JWT payload containing user_id and role\n   * @returns string - Signed JWT token\n   */\n  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {\n    try {\n      return jwt.sign(payload, getJWTSecret(), {\n        expiresIn: JWT_EXPIRES_IN,\n        algorithm: 'HS256'\n      });\n    } catch (error) {\n      throw new Error('Failed to generate JWT token');\n    }\n  }\n\n  /**\n   * Verify and decode a JWT token\n   * @param token - JWT token to verify\n   * @returns JWTPayload - Decoded payload\n   */\n  static verifyToken(token: string): JWTPayload {\n    try {\n      const decoded = jwt.verify(token, getJWTSecret(), {\n        algorithms: ['HS256']\n      }) as JWTPayload;\n      return decoded;\n    } catch (error) {\n      if (error instanceof jwt.TokenExpiredError) {\n        throw new Error('Token has expired');\n      } else if (error instanceof jwt.JsonWebTokenError) {\n        throw new Error('Invalid token');\n      } else {\n        throw new Error('Token verification failed');\n      }\n    }\n  }\n\n  /**\n   * Extract token from Authorization header\n   * @param authHeader - Authorization header value\n   * @returns string | null - Extracted token or null\n   */\n  static extractTokenFromHeader(authHeader: string | null): string | null {\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null;\n    }\n    return authHeader.substring(7); // Remove 'Bearer ' prefix\n  }\n}\n\n// Cookie configuration constants\nexport const COOKIE_CONFIG = {\n  NAME: 'haq_auth_token',\n  OPTIONS: {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'strict' as const,\n    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds\n    path: '/'\n  }\n};\n\n// Input validation utilities\nexport class ValidationUtils {\n  /**\n   * Validate email format\n   * @param email - Email to validate\n   * @returns boolean - True if valid\n   */\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n    return emailRegex.test(email) && email.length <= 255;\n  }\n\n  /**\n   * Validate username format\n   * @param username - Username to validate\n   * @returns boolean - True if valid\n   */\n  static isValidUsername(username: string): boolean {\n    // Username: 3-50 characters, alphanumeric and underscores only\n    const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;\n    return usernameRegex.test(username);\n  }\n\n  /**\n   * Validate password strength\n   * @param password - Password to validate\n   * @returns { isValid: boolean, errors: string[] }\n   */\n  static validatePassword(password: string): { isValid: boolean; errors: string[] } {\n    const errors: string[] = [];\n\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n\n    if (password.length > 128) {\n      errors.push('Password must be less than 128 characters');\n    }\n\n    if (!/[a-z]/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n\n    if (!/[A-Z]/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n\n    if (!/[0-9]/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('Password must contain at least one special character');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Sanitize input string to prevent XSS\n   * @param input - Input string to sanitize\n   * @returns string - Sanitized string\n   */\n  static sanitizeInput(input: string): string {\n    return input\n      .trim()\n      .replace(/[<>]/g, '') // Remove potential HTML tags\n      .substring(0, 1000); // Limit length\n  }\n}\n\n// Authentication helper functions (client-side)\nexport class AuthHelpers {\n  /**\n   * Verify JWT token and extract payload\n   * @param token - JWT token to verify\n   * @returns JWTPayload | null - Decoded payload or null if invalid\n   */\n  static verifyTokenSafe(token: string): JWTPayload | null {\n    try {\n      return JWTUtils.verifyToken(token);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Check if user has admin role from token\n   * @param token - JWT token\n   * @returns boolean - True if user is admin\n   */\n  static isAdminFromToken(token: string): boolean {\n    try {\n      const payload = JWTUtils.verifyToken(token);\n      return payload.role === 'admin';\n    } catch (error) {\n      return false;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEA,uDAAuD;AACvD,MAAM,eAAe;IACnB,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;IACrC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AA2B9C,MAAM;IACX,OAAwB,cAAc,GAAG;IAEzC;;;;GAIC,GACD,aAAa,aAAa,QAAgB,EAAmB;QAC3D,IAAI;YACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;YAClD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;;GAKC,GACD,aAAa,eAAe,QAAgB,EAAE,IAAY,EAAoB;QAC5E,IAAI;YACF,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;QACxC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,cAAc,OAAwC,EAAU;QACrE,IAAI;YACF,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,gBAAgB;gBACvC,WAAW;gBACX,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;GAIC,GACD,OAAO,YAAY,KAAa,EAAc;QAC5C,IAAI;YACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,gBAAgB;gBAChD,YAAY;oBAAC;iBAAQ;YACvB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBACjD,MAAM,IAAI,MAAM;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA;;;;GAIC,GACD,OAAO,uBAAuB,UAAyB,EAAiB;QACtE,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QACA,OAAO,WAAW,SAAS,CAAC,IAAI,0BAA0B;IAC5D;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;IACN,SAAS;QACP,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,IAAI,KAAK,KAAK;QACtB,MAAM;IACR;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,aAAa,KAAa,EAAW;QAC1C,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,MAAM,IAAI;IACnD;IAEA;;;;GAIC,GACD,OAAO,gBAAgB,QAAgB,EAAW;QAChD,+DAA+D;QAC/D,MAAM,gBAAgB;QACtB,OAAO,cAAc,IAAI,CAAC;IAC5B;IAEA;;;;GAIC,GACD,OAAO,iBAAiB,QAAgB,EAA0C;QAChF,MAAM,SAAmB,EAAE;QAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,SAAS,MAAM,GAAG,KAAK;YACzB,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,wCAAwC,IAAI,CAAC,WAAW;YAC3D,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;IAEA;;;;GAIC,GACD,OAAO,cAAc,KAAa,EAAU;QAC1C,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;SAClD,SAAS,CAAC,GAAG,OAAO,eAAe;IACxC;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,gBAAgB,KAAa,EAAqB;QACvD,IAAI;YACF,OAAO,SAAS,WAAW,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,OAAO,iBAAiB,KAAa,EAAW;QAC9C,IAAI;YACF,MAAM,UAAU,SAAS,WAAW,CAAC;YACrC,OAAO,QAAQ,IAAI,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/auth-server.ts"], "sourcesContent": ["import { cookies } from 'next/headers';\nimport { JW<PERSON>tils, COOKIE_CONFIG, type User, type JWTPayload } from './auth';\n\n// Direct database query function using service role\nasync function queryDatabase(sql: string, params: any[] = []): Promise<any> {\n  try {\n    const response = await fetch(`https://wqbuilazpyxpwyuwuqpi.supabase.co/rest/v1/rpc/exec_sql`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,\n        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!\n      },\n      body: JSON.stringify({ sql, args: params })\n    });\n\n    if (!response.ok) {\n      throw new Error(`Database query failed: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Database query error:', error);\n    throw error;\n  }\n}\n\n// Server-side cookie management utilities\nexport class ServerCookieUtils {\n  /**\n   * Set authentication cookie (server-side)\n   * @param token - JWT token to store\n   */\n  static async setAuthCookie(token: string): Promise<void> {\n    const cookieStore = await cookies();\n    cookieStore.set(COOKIE_CONFIG.NAME, token, COOKIE_CONFIG.OPTIONS);\n  }\n\n  /**\n   * Get authentication token from cookie (server-side)\n   * @returns Promise<string | null> - Token or null if not found\n   */\n  static async getAuthToken(): Promise<string | null> {\n    try {\n      const cookieStore = await cookies();\n      const cookie = cookieStore.get(COOKIE_CONFIG.NAME);\n      return cookie?.value || null;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Remove authentication cookie (server-side)\n   */\n  static async removeAuthCookie(): Promise<void> {\n    const cookieStore = await cookies();\n    cookieStore.delete(COOKIE_CONFIG.NAME);\n  }\n}\n\n// Server-side authentication helper functions\nexport class ServerAuthHelpers {\n  /**\n   * Get current user from request (server-side)\n   * @returns Promise<User | null> - Current user or null\n   */\n  static async getCurrentUser(): Promise<User | null> {\n    try {\n      const token = await ServerCookieUtils.getAuthToken();\n      if (!token) {\n        return null;\n      }\n\n      const payload = JWTUtils.verifyToken(token);\n      \n      // Fetch full user data using direct SQL query\n      const result = await queryDatabase(\n        'SELECT user_id, username, email, role, created_at, updated_at FROM haq_users_db.users WHERE user_id = $1 LIMIT 1',\n        [payload.user_id]\n      );\n\n      const users = result || [];\n      const error = null;\n\n      if (error || !users || users.length === 0) {\n        return null;\n      }\n\n      return users[0] as User;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Check if user is authenticated (server-side)\n   * @returns Promise<boolean> - True if authenticated\n   */\n  static async isAuthenticated(): Promise<boolean> {\n    const user = await this.getCurrentUser();\n    return user !== null;\n  }\n\n  /**\n   * Check if user has admin role (server-side)\n   * @returns Promise<boolean> - True if user is admin\n   */\n  static async isAdmin(): Promise<boolean> {\n    try {\n      const token = await ServerCookieUtils.getAuthToken();\n      if (!token) {\n        return false;\n      }\n\n      const payload = JWTUtils.verifyToken(token);\n      return payload.role === 'admin';\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Verify token and get user data (server-side)\n   * @param token - JWT token to verify\n   * @returns Promise<User | null> - User data or null if invalid\n   */\n  static async verifyTokenAndGetUser(token: string): Promise<User | null> {\n    try {\n      const payload = JWTUtils.verifyToken(token);\n      \n      // Fetch user data from database\n      const users = await queryDatabase(\n        'SELECT user_id, username, email, role, created_at, updated_at FROM haq_users_db.users WHERE user_id = $1 LIMIT 1',\n        [payload.user_id]\n      );\n      const error = null;\n\n      if (error || !users || users.length === 0) {\n        return null;\n      }\n\n      return users[0] as User;\n    } catch (error) {\n      return null;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,oDAAoD;AACpD,eAAe,cAAc,GAAW,EAAE,SAAgB,EAAE;IAC1D,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,6DAA6D,CAAC,EAAE;YAC5F,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,yBAAyB,EAAE;gBAClE,UAAU,QAAQ,GAAG,CAAC,yBAAyB;YACjD;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAK,MAAM;YAAO;QAC3C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,EAAE;QAC7D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM;IACX;;;GAGC,GACD,aAAa,cAAc,KAAa,EAAiB;QACvD,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,YAAY,GAAG,CAAC,oHAAA,CAAA,gBAAa,CAAC,IAAI,EAAE,OAAO,oHAAA,CAAA,gBAAa,CAAC,OAAO;IAClE;IAEA;;;GAGC,GACD,aAAa,eAAuC;QAClD,IAAI;YACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;YAChC,MAAM,SAAS,YAAY,GAAG,CAAC,oHAAA,CAAA,gBAAa,CAAC,IAAI;YACjD,OAAO,QAAQ,SAAS;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAa,mBAAkC;QAC7C,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,YAAY,MAAM,CAAC,oHAAA,CAAA,gBAAa,CAAC,IAAI;IACvC;AACF;AAGO,MAAM;IACX;;;GAGC,GACD,aAAa,iBAAuC;QAClD,IAAI;YACF,MAAM,QAAQ,MAAM,kBAAkB,YAAY;YAClD,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,MAAM,UAAU,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAErC,8CAA8C;YAC9C,MAAM,SAAS,MAAM,cACnB,oHACA;gBAAC,QAAQ,OAAO;aAAC;YAGnB,MAAM,QAAQ,UAAU,EAAE;YAC1B,MAAM,QAAQ;YAEd,IAAI,SAAS,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBACzC,OAAO;YACT;YAEA,OAAO,KAAK,CAAC,EAAE;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,aAAa,kBAAoC;QAC/C,MAAM,OAAO,MAAM,IAAI,CAAC,cAAc;QACtC,OAAO,SAAS;IAClB;IAEA;;;GAGC,GACD,aAAa,UAA4B;QACvC,IAAI;YACF,MAAM,QAAQ,MAAM,kBAAkB,YAAY;YAClD,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,MAAM,UAAU,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YACrC,OAAO,QAAQ,IAAI,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,aAAa,sBAAsB,KAAa,EAAwB;QACtE,IAAI;YACF,MAAM,UAAU,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAErC,gCAAgC;YAChC,MAAM,QAAQ,MAAM,cAClB,oHACA;gBAAC,QAAQ,OAAO;aAAC;YAEnB,MAAM,QAAQ;YAEd,IAAI,SAAS,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBACzC,OAAO;YACT;YAEA,OAAO,KAAK,CAAC,EAAE;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/admin-middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ServerAuthHelpers } from './auth-server';\n\n/**\n * Admin Authentication Middleware\n * Verifies JW<PERSON> token and ensures user has admin role\n * Following HAQ-rules.md ADMIN group requirements\n */\n\nexport interface AdminAuthResult {\n  success: boolean;\n  user?: any;\n  error?: string;\n  status?: number;\n}\n\n/**\n * Verify admin authentication for API routes\n * @param request - Next.js request object\n * @returns AdminAuthResult with success status and user data or error\n */\nexport async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {\n  try {\n    // Check if user is authenticated\n    const isAuthenticated = await ServerAuthHelpers.isAuthenticated();\n    if (!isAuthenticated) {\n      return {\n        success: false,\n        error: 'User is not authenticated',\n        status: 401\n      };\n    }\n\n    // Check if user has admin role\n    const isAdmin = await ServerAuthHelpers.isAdmin();\n    if (!isAdmin) {\n      return {\n        success: false,\n        error: 'Unauthorized access: User does not have admin privileges',\n        status: 401\n      };\n    }\n\n    // Get current user data\n    const user = await ServerAuthHelpers.getCurrentUser();\n    if (!user) {\n      return {\n        success: false,\n        error: 'Failed to retrieve user data',\n        status: 401\n      };\n    }\n\n    return {\n      success: true,\n      user\n    };\n  } catch (error) {\n    console.error('Admin auth verification error:', error);\n    return {\n      success: false,\n      error: 'Internal authentication error',\n      status: 500\n    };\n  }\n}\n\n/**\n * Higher-order function to wrap API route handlers with admin authentication\n * @param handler - The API route handler function\n * @returns Wrapped handler with admin auth check\n */\nexport function withAdminAuth(\n  handler: (request: NextRequest, context: any, user: any) => Promise<NextResponse>\n) {\n  return async (request: NextRequest, context: any = {}): Promise<NextResponse> => {\n    // Verify admin authentication\n    const authResult = await verifyAdminAuth(request);\n    \n    if (!authResult.success) {\n      return NextResponse.json(\n        { \n          success: false, \n          message: authResult.error || 'Authentication failed' \n        },\n        { status: authResult.status || 401 }\n      );\n    }\n\n    // Call the original handler with authenticated user\n    try {\n      return await handler(request, context, authResult.user);\n    } catch (error) {\n      console.error('Admin API handler error:', error);\n      return NextResponse.json(\n        { \n          success: false, \n          message: 'Internal server error' \n        },\n        { status: 500 }\n      );\n    }\n  };\n}\n\n/**\n * Middleware function for admin route protection\n * Can be used in middleware.ts for route-level protection\n */\nexport async function adminRouteMiddleware(request: NextRequest): Promise<NextResponse | null> {\n  // Only apply to admin routes\n  if (!request.nextUrl.pathname.startsWith('/admin')) {\n    return null; // Continue to next middleware\n  }\n\n  const authResult = await verifyAdminAuth(request);\n  \n  if (!authResult.success) {\n    // Redirect to login page with return URL\n    const loginUrl = new URL('/auth/login', request.url);\n    loginUrl.searchParams.set('returnUrl', request.nextUrl.pathname);\n    loginUrl.searchParams.set('error', 'admin_required');\n    \n    return NextResponse.redirect(loginUrl);\n  }\n\n  // Continue to the admin route\n  return NextResponse.next();\n}\n\n/**\n * Utility function to check admin status in server components\n * @returns Promise<boolean> - True if current user is admin\n */\nexport async function isCurrentUserAdmin(): Promise<boolean> {\n  try {\n    return await ServerAuthHelpers.isAdmin();\n  } catch (error) {\n    console.error('Error checking admin status:', error);\n    return false;\n  }\n}\n\n/**\n * Utility function to get current admin user in server components\n * @returns Promise<any | null> - Admin user data or null\n */\nexport async function getCurrentAdminUser(): Promise<any | null> {\n  try {\n    const isAdmin = await ServerAuthHelpers.isAdmin();\n    if (!isAdmin) {\n      return null;\n    }\n    \n    return await ServerAuthHelpers.getCurrentUser();\n  } catch (error) {\n    console.error('Error getting admin user:', error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAoBO,eAAe,gBAAgB,OAAoB;IACxD,IAAI;QACF,iCAAiC;QACjC,MAAM,kBAAkB,MAAM,8HAAA,CAAA,oBAAiB,CAAC,eAAe;QAC/D,IAAI,CAAC,iBAAiB;YACpB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,+BAA+B;QAC/B,MAAM,UAAU,MAAM,8HAAA,CAAA,oBAAiB,CAAC,OAAO;QAC/C,IAAI,CAAC,SAAS;YACZ,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,wBAAwB;QACxB,MAAM,OAAO,MAAM,8HAAA,CAAA,oBAAiB,CAAC,cAAc;QACnD,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,OAAO;YACL,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAOO,SAAS,cACd,OAAiF;IAEjF,OAAO,OAAO,SAAsB,UAAe,CAAC,CAAC;QACnD,8BAA8B;QAC9B,MAAM,aAAa,MAAM,gBAAgB;QAEzC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS,WAAW,KAAK,IAAI;YAC/B,GACA;gBAAE,QAAQ,WAAW,MAAM,IAAI;YAAI;QAEvC;QAEA,oDAAoD;QACpD,IAAI;YACF,OAAO,MAAM,QAAQ,SAAS,SAAS,WAAW,IAAI;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAMO,eAAe,qBAAqB,OAAoB;IAC7D,6BAA6B;IAC7B,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;QAClD,OAAO,MAAM,8BAA8B;IAC7C;IAEA,MAAM,aAAa,MAAM,gBAAgB;IAEzC,IAAI,CAAC,WAAW,OAAO,EAAE;QACvB,yCAAyC;QACzC,MAAM,WAAW,IAAI,IAAI,eAAe,QAAQ,GAAG;QACnD,SAAS,YAAY,CAAC,GAAG,CAAC,aAAa,QAAQ,OAAO,CAAC,QAAQ;QAC/D,SAAS,YAAY,CAAC,GAAG,CAAC,SAAS;QAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,8BAA8B;IAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAMO,eAAe;IACpB,IAAI;QACF,OAAO,MAAM,8HAAA,CAAA,oBAAiB,CAAC,OAAO;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,8HAAA,CAAA,oBAAiB,CAAC,OAAO;QAC/C,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,OAAO,MAAM,8HAAA,CAAA,oBAAiB,CAAC,cAAc;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\n// Supabase configuration\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Singleton pattern to prevent multiple client instances\nlet supabaseInstance: ReturnType<typeof createBrowserClient> | null = null\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  if (!supabaseInstance) {\n    supabaseInstance = createBrowserClient(supabaseUrl, supabaseAnonKey)\n  }\n  return supabaseInstance\n}\n\n// Legacy client for backward compatibility\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Create separate clients for different schemas\nexport const supabaseUsers = createClient(supabaseUrl, supabaseAnonKey, {\n  db: { schema: 'haq_users_db' }\n})\n\nexport const supabaseContent = createClient(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>, {\n  db: { schema: 'haq_content_db' }\n})\n\n// Database types (will be generated later)\nexport type Database = {\n  haq_users_db: {\n    Tables: {\n      users: {\n        Row: {\n          user_id: string\n          username: string\n          email: string\n          password_hash: string\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          user_id?: string\n          username: string\n          email: string\n          password_hash: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          user_id?: string\n          username?: string\n          email?: string\n          password_hash?: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n  haq_content_db: {\n    Tables: {\n      companies: {\n        Row: {\n          company_id: string\n          name: string\n          slug: string\n          industry: string | null\n          location: string | null\n          description: string | null\n          website_url: string | null\n          logo_url: string | null\n          employee_count_range: string | null\n          founded_year: number | null\n          haq_score: number\n          total_reviews: number\n          is_verified: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          company_id?: string\n          name: string\n          slug: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          company_id?: string\n          name?: string\n          slug?: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      reviews: {\n        Row: {\n          review_id: string\n          company_id: string\n          anonymous_user_hash: string\n          overall_rating: number | null\n          work_life_balance_rating: number | null\n          compensation_rating: number | null\n          management_rating: number | null\n          culture_rating: number | null\n          title: string\n          pros: string | null\n          cons: string | null\n          advice_to_management: string | null\n          job_title: string | null\n          employment_status: 'current' | 'former' | null\n          employment_duration: string | null\n          department: string | null\n          location: string | null\n          is_approved: boolean\n          is_featured: boolean\n          helpful_count: number\n          created_at: string\n          updated_at: string\n        }\n      }\n      salary_reports: {\n        Row: {\n          salary_id: string\n          company_id: string\n          anonymous_user_hash: string\n          job_title: string\n          department: string | null\n          location: string | null\n          experience_level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive' | null\n          base_salary: number | null\n          bonus: number\n          stock_options: number\n          total_compensation: number | null\n          currency: string\n          employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | null\n          years_of_experience: number | null\n          years_at_company: number | null\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n      }\n      company_flags: {\n        Row: {\n          flag_id: string\n          company_id: string\n          flag_type: 'red' | 'green' | null\n          flag_text: string\n          flag_count: number\n          created_at: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AAEA,yBAAyB;AACzB,MAAM;AACN,MAAM;AAEN,yDAAyD;AACzD,IAAI,mBAAkE;AAG/D,MAAM,8BAA8B;IACzC,IAAI,CAAC,kBAAkB;QACrB,mBAAmB,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IACtD;IACA,OAAO;AACT;AAGO,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACtE,IAAI;QAAE,QAAQ;IAAe;AAC/B;AAEO,MAAM,kBAAkB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACxE,IAAI;QAAE,QAAQ;IAAiB;AACjC", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/api/admin/companies/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { withAdminAuth } from '@/lib/admin-middleware';\nimport { supabaseContent } from '@/lib/supabase';\nimport { z } from 'zod';\n\n// Validation schema for company creation\nconst createCompanySchema = z.object({\n  name: z.string().min(1, 'Company name is required').max(255, 'Company name too long'),\n  industry: z.string().optional(),\n  hq_location: z.string().optional(),\n  description: z.string().optional(),\n  website_url: z.string().url('Invalid website URL').optional().or(z.literal('')),\n  logo_url: z.string().url('Invalid logo URL').optional().or(z.literal('')),\n  employee_count_range: z.string().optional(),\n  founded_year: z.number().int().min(1800).max(new Date().getFullYear()).optional(),\n});\n\n/**\n * GET /api/admin/companies\n * Retrieve all companies for admin dashboard\n * Requires admin authentication\n */\nasync function getCompanies(request: NextRequest, context: any, user: any): Promise<NextResponse> {\n  try {\n    // Get query parameters for pagination and filtering\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const search = searchParams.get('search') || '';\n    const industry = searchParams.get('industry') || '';\n\n    // Calculate offset for pagination\n    const offset = (page - 1) * limit;\n\n    // Build query\n    let query = supabaseContent\n      .from('companies')\n      .select(`\n        company_id,\n        name,\n        slug,\n        industry,\n        location,\n        description,\n        website_url,\n        logo_url,\n        employee_count_range,\n        founded_year,\n        haq_score,\n        total_reviews,\n        is_verified,\n        created_at,\n        updated_at\n      `)\n      .order('created_at', { ascending: false });\n\n    // Apply search filter\n    if (search) {\n      query = query.ilike('name', `%${search}%`);\n    }\n\n    // Apply industry filter\n    if (industry) {\n      query = query.eq('industry', industry);\n    }\n\n    // Apply pagination\n    query = query.range(offset, offset + limit - 1);\n\n    const { data: companies, error, count } = await query;\n\n    if (error) {\n      console.error('Error fetching companies:', error);\n      return NextResponse.json(\n        { success: false, message: 'Failed to fetch companies' },\n        { status: 500 }\n      );\n    }\n\n    // Get total count for pagination\n    const { count: totalCount } = await supabaseContent\n      .from('companies')\n      .select('*', { count: 'exact', head: true });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        companies: companies || [],\n        pagination: {\n          page,\n          limit,\n          total: totalCount || 0,\n          totalPages: Math.ceil((totalCount || 0) / limit)\n        }\n      }\n    });\n\n  } catch (error) {\n    console.error('Get companies error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST /api/admin/companies\n * Create a new company profile\n * Requires admin authentication\n */\nasync function createCompany(request: NextRequest, context: any, user: any): Promise<NextResponse> {\n  try {\n    const body = await request.json();\n\n    // Validate request body\n    const validationResult = createCompanySchema.safeParse(body);\n    if (!validationResult.success) {\n      return NextResponse.json(\n        { \n          success: false, \n          message: 'Validation failed',\n          errors: validationResult.error.errors\n        },\n        { status: 400 }\n      );\n    }\n\n    const companyData = validationResult.data;\n\n    // Generate slug from company name\n    const slug = companyData.name\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, '-')\n      .replace(/^-+|-+$/g, '');\n\n    // Check if company name or slug already exists\n    const { data: existingCompany } = await supabaseContent\n      .from('companies')\n      .select('company_id, name, slug')\n      .or(`name.eq.${companyData.name},slug.eq.${slug}`)\n      .limit(1);\n\n    if (existingCompany && existingCompany.length > 0) {\n      return NextResponse.json(\n        { \n          success: false, \n          message: 'Company with this name already exists' \n        },\n        { status: 409 }\n      );\n    }\n\n    // Insert new company\n    const { data: newCompany, error } = await supabaseContent\n      .from('companies')\n      .insert({\n        name: companyData.name,\n        slug,\n        industry: companyData.industry || null,\n        location: companyData.hq_location || null,\n        description: companyData.description || null,\n        website_url: companyData.website_url || null,\n        logo_url: companyData.logo_url || null,\n        employee_count_range: companyData.employee_count_range || null,\n        founded_year: companyData.founded_year || null,\n        haq_score: 0.00,\n        total_reviews: 0,\n        is_verified: false\n      })\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating company:', error);\n      return NextResponse.json(\n        { success: false, message: 'Failed to create company' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'Company created successfully',\n      data: newCompany\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Create company error:', error);\n    return NextResponse.json(\n      { success: false, message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// Export wrapped handlers with admin authentication\nexport const GET = withAdminAuth(getCompanies);\nexport const POST = withAdminAuth(createCompany);\n\n// Handle unsupported methods\nexport async function PUT() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n\nexport async function DELETE() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n\nexport async function PATCH() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AAAA;;;;;AAEA,yCAAyC;AACzC,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,KAAK;IAC7D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,uBAAuB,QAAQ,GAAG,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAC3E,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,oBAAoB,QAAQ,GAAG,EAAE,CAAC,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACrE,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI,QAAQ;AACjF;AAEA;;;;CAIC,GACD,eAAe,aAAa,OAAoB,EAAE,OAAY,EAAE,IAAS;IACvE,IAAI;QACF,oDAAoD;QACpD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QAEjD,kCAAkC;QAClC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,IAAI,QAAQ,wHAAA,CAAA,kBAAe,CACxB,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;MAgBT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,sBAAsB;QACtB,IAAI,QAAQ;YACV,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC3C;QAEA,wBAAwB;QACxB,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QAEA,mBAAmB;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAE7C,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEhD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA4B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,wHAAA,CAAA,kBAAe,CAChD,IAAI,CAAC,aACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW,aAAa,EAAE;gBAC1B,YAAY;oBACV;oBACA;oBACA,OAAO,cAAc;oBACrB,YAAY,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI;gBAC5C;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA;;;;CAIC,GACD,eAAe,cAAc,OAAoB,EAAE,OAAY,EAAE,IAAS;IACxE,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,wBAAwB;QACxB,MAAM,mBAAmB,oBAAoB,SAAS,CAAC;QACvD,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;gBACT,QAAQ,iBAAiB,KAAK,CAAC,MAAM;YACvC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,iBAAiB,IAAI;QAEzC,kCAAkC;QAClC,MAAM,OAAO,YAAY,IAAI,CAC1B,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;QAEvB,+CAA+C;QAC/C,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,wHAAA,CAAA,kBAAe,CACpD,IAAI,CAAC,aACL,MAAM,CAAC,0BACP,EAAE,CAAC,CAAC,QAAQ,EAAE,YAAY,IAAI,CAAC,SAAS,EAAE,MAAM,EAChD,KAAK,CAAC;QAET,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;YACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,kBAAe,CACtD,IAAI,CAAC,aACL,MAAM,CAAC;YACN,MAAM,YAAY,IAAI;YACtB;YACA,UAAU,YAAY,QAAQ,IAAI;YAClC,UAAU,YAAY,WAAW,IAAI;YACrC,aAAa,YAAY,WAAW,IAAI;YACxC,aAAa,YAAY,WAAW,IAAI;YACxC,UAAU,YAAY,QAAQ,IAAI;YAClC,sBAAsB,YAAY,oBAAoB,IAAI;YAC1D,cAAc,YAAY,YAAY,IAAI;YAC1C,WAAW;YACX,eAAe;YACf,aAAa;QACf,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA2B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,MAAM,MAAM,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;AAC1B,MAAM,OAAO,CAAA,GAAA,mIAAA,CAAA,gBAAa,AAAD,EAAE;AAG3B,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;IAAI;AAElB;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;IAAI;AAElB;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}