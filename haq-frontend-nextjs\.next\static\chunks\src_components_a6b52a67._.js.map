{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/common/CompanyCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Star, MapPin, Users, AlertTriangle, CheckCircle } from 'lucide-react';\n\ninterface CompanyCardProps {\n  company: {\n    company_id?: string; // New UUID format\n    id?: number; // Legacy support\n    name: string;\n    logo_url?: string;\n    haq_score: number;\n    total_reviews?: number;\n    industry?: string;\n    location?: string;\n    redFlags?: string[];\n    greenFlags?: string[];\n  };\n  delay?: number;\n}\n\nexport const CompanyCard: React.FC<CompanyCardProps> = ({ company, delay = 0 }) => {\n  const getScoreColor = (score: number) => {\n    if (score >= 4.0) return 'text-green-400';\n    if (score >= 3.0) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  const getScoreBgColor = (score: number) => {\n    if (score >= 4.0) return 'bg-green-400/20';\n    if (score >= 3.0) return 'bg-yellow-400/20';\n    return 'bg-red-400/20';\n  };\n\n  // Use company_id if available, fallback to legacy id\n  const companyId = company.company_id || company.id;\n  const companyUrl = `/companies/${companyId}`;\n\n  return (\n    <Link href={companyUrl}>\n      <div \n        className=\"bg-surface-primary border border-border-primary rounded-medium p-6 hover:border-accent-primary transition-all duration-200 animate-slide-up group cursor-pointer\"\n        style={{ animationDelay: `${delay}ms` }}\n      >\n        {/* Header */}\n        <div className=\"flex items-start space-x-4 mb-4\">\n          <div className=\"relative w-12 h-12 rounded-lg overflow-hidden bg-surface-secondary flex-shrink-0\">\n            <Image\n              src={company.logo_url || \"/placeholder-company.svg\"}\n              alt={`${company.name} logo`}\n              fill\n              className=\"object-cover\"\n              sizes=\"48px\"\n            />\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <h3 className=\"text-lg font-semibold text-text-primary group-hover:text-accent-primary transition-colors duration-200 truncate\">\n              {company.name}\n            </h3>\n            <div className=\"flex items-center space-x-4 text-sm text-text-secondary\">\n              {company.industry && <span>{company.industry}</span>}\n              {company.location && (\n                <div className=\"flex items-center space-x-1\">\n                  <MapPin className=\"w-3 h-3\" />\n                  <span>{company.location}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Haq Score */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className={`px-3 py-1 rounded-lg ${getScoreBgColor(company.haq_score || 0)}`}>\n              <span className={`font-bold ${getScoreColor(company.haq_score || 0)}`}>\n                {(company.haq_score || 0).toFixed(1)}\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Star className={`w-4 h-4 ${getScoreColor(company.haq_score || 0)}`} fill=\"currentColor\" />\n              <span className=\"text-text-secondary text-sm\">Haq Score</span>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-1 text-text-secondary text-sm\">\n            <Users className=\"w-4 h-4\" />\n            <span>{company.total_reviews || 0} reviews</span>\n          </div>\n        </div>\n\n        {/* Flags */}\n        <div className=\"space-y-3\">\n          {/* Green Flags */}\n          {company.greenFlags && company.greenFlags.length > 0 && (\n            <div>\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                <span className=\"text-sm font-medium text-text-primary\">Positives</span>\n              </div>\n              <div className=\"flex flex-wrap gap-1\">\n                {company.greenFlags.slice(0, 2).map((flag, index) => (\n                  <span\n                    key={index}\n                    className=\"px-2 py-1 bg-green-400/20 text-green-400 text-xs rounded-lg\"\n                  >\n                    {flag}\n                  </span>\n                ))}\n                {company.greenFlags.length > 2 && (\n                  <span className=\"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg\">\n                    +{company.greenFlags.length - 2} more\n                  </span>\n                )}\n              </div>\n            </div>\n          )}\n\n          {/* Red Flags */}\n          {company.redFlags && company.redFlags.length > 0 && (\n            <div>\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <AlertTriangle className=\"w-4 h-4 text-red-400\" />\n                <span className=\"text-sm font-medium text-text-primary\">Issues</span>\n              </div>\n              <div className=\"flex flex-wrap gap-1\">\n                {company.redFlags.slice(0, 2).map((flag, index) => (\n                  <span\n                    key={index}\n                    className=\"px-2 py-1 bg-red-400/20 text-red-400 text-xs rounded-lg\"\n                  >\n                    {flag}\n                  </span>\n                ))}\n                {company.redFlags.length > 2 && (\n                  <span className=\"px-2 py-1 bg-surface-secondary text-text-secondary text-xs rounded-lg\">\n                    +{company.redFlags.length - 2} more\n                  </span>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* View Details */}\n        <div className=\"mt-4 pt-4 border-t border-border-primary\">\n          <span className=\"text-accent-primary text-sm font-medium group-hover:underline\">\n            View Details →\n          </span>\n        </div>\n      </div>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAkBO,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;IAC5E,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,EAAE;IAClD,MAAM,aAAa,CAAC,WAAW,EAAE,WAAW;IAE5C,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;kBACV,cAAA,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,gBAAgB,GAAG,MAAM,EAAE,CAAC;YAAC;;8BAGtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,QAAQ,IAAI;gCACzB,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC;gCAC3B,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,QAAQ,IAAI;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,QAAQ,kBAAI,6LAAC;sDAAM,QAAQ,QAAQ;;;;;;wCAC3C,QAAQ,QAAQ,kBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAM,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,QAAQ,SAAS,IAAI,IAAI;8CAC/E,cAAA,6LAAC;wCAAK,WAAW,CAAC,UAAU,EAAE,cAAc,QAAQ,SAAS,IAAI,IAAI;kDAClE,CAAC,QAAQ,SAAS,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;8CAGtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,QAAQ,SAAS,IAAI,IAAI;4CAAE,MAAK;;;;;;sDAC1E,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAGlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;;wCAAM,QAAQ,aAAa,IAAI;wCAAE;;;;;;;;;;;;;;;;;;;8BAKtC,6LAAC;oBAAI,WAAU;;wBAEZ,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,mBACjD,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACzC,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,QAAQ,UAAU,CAAC,MAAM,GAAG,mBAC3B,6LAAC;4CAAK,WAAU;;gDAAwE;gDACpF,QAAQ,UAAU,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;wBAQzC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAK,WAAU;sDAAwC;;;;;;;;;;;;8CAE1D,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACvC,6LAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;wCAMR,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC;4CAAK,WAAU;;gDAAwE;gDACpF,QAAQ,QAAQ,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;8BAS1C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgE;;;;;;;;;;;;;;;;;;;;;;AAO1F;KAnIa", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/companies/CompaniesListClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { Search, Filter, Building2, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { CompanyCard } from '@/components/common/CompanyCard';\n\ninterface Company {\n  company_id: string;\n  name: string;\n  slug: string;\n  industry?: string;\n  location?: string;\n  website_url?: string;\n  logo_url?: string;\n  employee_count_range?: string;\n  founded_year?: number;\n  haq_score: number;\n  total_reviews: number;\n  is_verified: boolean;\n}\n\ninterface CompaniesData {\n  companies: Company[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNext: boolean;\n    hasPrev: boolean;\n  };\n  search: string | null;\n}\n\nconst CompaniesListClient: React.FC = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const [companiesData, setCompaniesData] = useState<CompaniesData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');\n  const [showFilters, setShowFilters] = useState(false);\n\n  const currentPage = parseInt(searchParams.get('page') || '1');\n  const currentSearch = searchParams.get('q') || '';\n\n  // Fetch companies data\n  const fetchCompanies = async (page: number = 1, search: string = '') => {\n    setIsLoading(true);\n    try {\n      const params = new URLSearchParams();\n      params.append('page', page.toString());\n      params.append('limit', '12');\n      if (search.trim()) {\n        params.append('q', search.trim());\n      }\n\n      const response = await fetch(`/api/companies?${params}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setCompaniesData(result.data);\n      } else {\n        console.error('Failed to fetch companies:', result.message);\n        setCompaniesData(null);\n      }\n    } catch (error) {\n      console.error('Error fetching companies:', error);\n      setCompaniesData(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Load companies on component mount and when search params change\n  useEffect(() => {\n    fetchCompanies(currentPage, currentSearch);\n  }, [currentPage, currentSearch]);\n\n  // Update search query state when URL changes\n  useEffect(() => {\n    setSearchQuery(currentSearch);\n  }, [currentSearch]);\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    const params = new URLSearchParams(searchParams);\n    \n    if (searchQuery.trim()) {\n      params.set('q', searchQuery.trim());\n    } else {\n      params.delete('q');\n    }\n    params.delete('page'); // Reset to page 1 when searching\n    \n    router.push(`/companies?${params.toString()}`);\n  };\n\n  const handlePageChange = (newPage: number) => {\n    const params = new URLSearchParams(searchParams);\n    params.set('page', newPage.toString());\n    router.push(`/companies?${params.toString()}`);\n  };\n\n  const renderPagination = () => {\n    if (!companiesData?.pagination || companiesData.pagination.totalPages <= 1) {\n      return null;\n    }\n\n    const { pagination } = companiesData;\n    const pages = [];\n    const maxVisiblePages = 5;\n    \n    let startPage = Math.max(1, pagination.page - Math.floor(maxVisiblePages / 2));\n    let endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);\n    \n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return (\n      <div className=\"flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-8\">\n        <div className=\"flex flex-1 justify-between sm:hidden\">\n          <button\n            onClick={() => handlePageChange(pagination.page - 1)}\n            disabled={!pagination.hasPrev}\n            className=\"relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Previous\n          </button>\n          <button\n            onClick={() => handlePageChange(pagination.page + 1)}\n            disabled={!pagination.hasNext}\n            className=\"relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Next\n          </button>\n        </div>\n        \n        <div className=\"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between\">\n          <div>\n            <p className=\"text-sm text-gray-700\">\n              Showing{' '}\n              <span className=\"font-medium\">\n                {(pagination.page - 1) * pagination.limit + 1}\n              </span>{' '}\n              to{' '}\n              <span className=\"font-medium\">\n                {Math.min(pagination.page * pagination.limit, pagination.total)}\n              </span>{' '}\n              of{' '}\n              <span className=\"font-medium\">{pagination.total}</span> results\n            </p>\n          </div>\n          \n          <div>\n            <nav className=\"isolate inline-flex -space-x-px rounded-md shadow-sm\" aria-label=\"Pagination\">\n              <button\n                onClick={() => handlePageChange(pagination.page - 1)}\n                disabled={!pagination.hasPrev}\n                className=\"relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ChevronLeft className=\"h-5 w-5\" />\n              </button>\n              \n              {pages.map((page) => (\n                <button\n                  key={page}\n                  onClick={() => handlePageChange(page)}\n                  className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${\n                    page === pagination.page\n                      ? 'z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'\n                      : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'\n                  }`}\n                >\n                  {page}\n                </button>\n              ))}\n              \n              <button\n                onClick={() => handlePageChange(pagination.page + 1)}\n                disabled={!pagination.hasNext}\n                className=\"relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ChevronRight className=\"h-5 w-5\" />\n              </button>\n            </nav>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <form onSubmit={handleSearch} className=\"mb-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search companies by name or industry...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n        </form>\n\n        <div className=\"flex items-center justify-between\">\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <Filter className=\"h-4 w-4\" />\n            <span>Filters</span>\n          </button>\n\n          {companiesData && (\n            <div className=\"text-sm text-gray-600\">\n              {companiesData.pagination.total} companies found\n              {companiesData.search && (\n                <span> for \"{companiesData.search}\"</span>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Companies Grid */}\n      {isLoading ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {Array.from({ length: 6 }).map((_, index) => (\n            <div key={index} className=\"bg-white rounded-lg shadow-sm p-6 animate-pulse\">\n              <div className=\"flex items-start space-x-4 mb-4\">\n                <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <div className=\"h-3 bg-gray-200 rounded w-full\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : companiesData && companiesData.companies.length > 0 ? (\n        <>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {companiesData.companies.map((company, index) => (\n              <CompanyCard \n                key={company.company_id} \n                company={company} \n                delay={index * 50} \n              />\n            ))}\n          </div>\n          {renderPagination()}\n        </>\n      ) : (\n        <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n          <Building2 className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No companies found</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {companiesData?.search \n              ? `No companies match your search for \"${companiesData.search}\".`\n              : 'No companies are available at the moment.'\n            }\n          </p>\n          {companiesData?.search && (\n            <button\n              onClick={() => {\n                setSearchQuery('');\n                router.push('/companies');\n              }}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Clear Search\n            </button>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CompaniesListClient;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAmCA,MAAM,sBAAgC;;IACpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,GAAG,CAAC,QAAQ;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc,SAAS,aAAa,GAAG,CAAC,WAAW;IACzD,MAAM,gBAAgB,aAAa,GAAG,CAAC,QAAQ;IAE/C,uBAAuB;IACvB,MAAM,iBAAiB,OAAO,OAAe,CAAC,EAAE,SAAiB,EAAE;QACjE,aAAa;QACb,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;YACnC,OAAO,MAAM,CAAC,SAAS;YACvB,IAAI,OAAO,IAAI,IAAI;gBACjB,OAAO,MAAM,CAAC,KAAK,OAAO,IAAI;YAChC;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,QAAQ;YACvD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,OAAO,IAAI;YAC9B,OAAO;gBACL,QAAQ,KAAK,CAAC,8BAA8B,OAAO,OAAO;gBAC1D,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,iBAAiB;QACnB,SAAU;YACR,aAAa;QACf;IACF;IAEA,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,eAAe,aAAa;QAC9B;wCAAG;QAAC;QAAa;KAAc;IAE/B,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,eAAe;QACjB;wCAAG;QAAC;KAAc;IAElB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,SAAS,IAAI,gBAAgB;QAEnC,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,GAAG,CAAC,KAAK,YAAY,IAAI;QAClC,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QACA,OAAO,MAAM,CAAC,SAAS,iCAAiC;QAExD,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,QAAQ,IAAI;IAC/C;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,IAAI,gBAAgB;QACnC,OAAO,GAAG,CAAC,QAAQ,QAAQ,QAAQ;QACnC,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,QAAQ,IAAI;IAC/C;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,eAAe,cAAc,cAAc,UAAU,CAAC,UAAU,IAAI,GAAG;YAC1E,OAAO;QACT;QAEA,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,MAAM,QAAQ,EAAE;QAChB,MAAM,kBAAkB;QAExB,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW,IAAI,GAAG,KAAK,KAAK,CAAC,kBAAkB;QAC3E,IAAI,UAAU,KAAK,GAAG,CAAC,WAAW,UAAU,EAAE,YAAY,kBAAkB;QAE5E,IAAI,UAAU,YAAY,IAAI,iBAAiB;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,kBAAkB;QACtD;QAEA,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;4BAClD,UAAU,CAAC,WAAW,OAAO;4BAC7B,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;4BAClD,UAAU,CAAC,WAAW,OAAO;4BAC7B,WAAU;sCACX;;;;;;;;;;;;8BAKH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCACC,cAAA,6LAAC;gCAAE,WAAU;;oCAAwB;oCAC3B;kDACR,6LAAC;wCAAK,WAAU;kDACb,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAG;;;;;;oCACtC;oCAAI;oCACT;kDACH,6LAAC;wCAAK,WAAU;kDACb,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;;;;;;oCACxD;oCAAI;oCACT;kDACH,6LAAC;wCAAK,WAAU;kDAAe,WAAW,KAAK;;;;;;oCAAQ;;;;;;;;;;;;sCAI3D,6LAAC;sCACC,cAAA,6LAAC;gCAAI,WAAU;gCAAuD,cAAW;;kDAC/E,6LAAC;wCACC,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;wCAClD,UAAU,CAAC,WAAW,OAAO;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;oCAGxB,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4CAEC,SAAS,IAAM,iBAAiB;4CAChC,WAAW,CAAC,kEAAkE,EAC5E,SAAS,WAAW,IAAI,GACpB,uJACA,oGACJ;sDAED;2CARI;;;;;kDAYT,6LAAC;wCACC,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;wCAClD,UAAU,CAAC,WAAW,OAAO;wCAC7B,WAAU;kDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOtC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,UAAU;wBAAc,WAAU;kCACtC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;4BAGP,+BACC,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,UAAU,CAAC,KAAK;oCAAC;oCAC/B,cAAc,MAAM,kBACnB,6LAAC;;4CAAK;4CAAO,cAAc,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3C,0BACC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;uBAVT;;;;;;;;;uBAeZ,iBAAiB,cAAc,SAAS,CAAC,MAAM,GAAG,kBACpD;;kCACE,6LAAC;wBAAI,WAAU;kCACZ,cAAc,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,sBACrC,6LAAC,8IAAA,CAAA,cAAW;gCAEV,SAAS;gCACT,OAAO,QAAQ;+BAFV,QAAQ,UAAU;;;;;;;;;;oBAM5B;;6CAGH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,eAAe,SACZ,CAAC,oCAAoC,EAAE,cAAc,MAAM,CAAC,EAAE,CAAC,GAC/D;;;;;;oBAGL,eAAe,wBACd,6LAAC;wBACC,SAAS;4BACP,eAAe;4BACf,OAAO,IAAI,CAAC;wBACd;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAQb;GAhQM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAFhC;uCAkQS", "debugId": null}}]}