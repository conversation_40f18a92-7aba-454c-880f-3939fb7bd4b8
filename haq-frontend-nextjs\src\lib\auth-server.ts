import { cookies } from 'next/headers';
import { J<PERSON><PERSON><PERSON><PERSON>, COOKIE_CONFIG, type User, type JWTPayload } from './auth';
import { supabase } from './supabase';

// Server-side cookie management utilities
export class ServerCookieUtils {
  /**
   * Set authentication cookie (server-side)
   * @param token - JWT token to store
   */
  static async setAuthCookie(token: string): Promise<void> {
    const cookieStore = await cookies();
    cookieStore.set(COOKIE_CONFIG.NAME, token, COOKIE_CONFIG.OPTIONS);
  }

  /**
   * Get authentication token from cookie (server-side)
   * @returns Promise<string | null> - Token or null if not found
   */
  static async getAuthToken(): Promise<string | null> {
    try {
      const cookieStore = await cookies();
      const cookie = cookieStore.get(COOKIE_CONFIG.NAME);
      return cookie?.value || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Remove authentication cookie (server-side)
   */
  static async removeAuthCookie(): Promise<void> {
    const cookieStore = await cookies();
    cookieStore.delete(COOKIE_CONFIG.NAME);
  }
}

// Server-side authentication helper functions
export class ServerAuthHelpers {
  /**
   * Get current user from request (server-side)
   * @returns Promise<User | null> - Current user or null
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const token = await ServerCookieUtils.getAuthToken();
      if (!token) {
        return null;
      }

      const payload = JWTUtils.verifyToken(token);
      
      // Fetch full user data from database
      const { data: users, error } = await supabase
        .from('haq_users_db.users')
        .select('user_id, username, email, role, created_at, updated_at')
        .eq('user_id', payload.user_id)
        .limit(1);

      if (error || !users || users.length === 0) {
        return null;
      }

      return users[0] as User;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if user is authenticated (server-side)
   * @returns Promise<boolean> - True if authenticated
   */
  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }

  /**
   * Check if user has admin role (server-side)
   * @returns Promise<boolean> - True if user is admin
   */
  static async isAdmin(): Promise<boolean> {
    try {
      const token = await ServerCookieUtils.getAuthToken();
      if (!token) {
        return false;
      }

      const payload = JWTUtils.verifyToken(token);
      return payload.role === 'admin';
    } catch (error) {
      return false;
    }
  }

  /**
   * Verify token and get user data (server-side)
   * @param token - JWT token to verify
   * @returns Promise<User | null> - User data or null if invalid
   */
  static async verifyTokenAndGetUser(token: string): Promise<User | null> {
    try {
      const payload = JWTUtils.verifyToken(token);
      
      // Fetch user data from database
      const { data: users, error } = await supabase
        .from('haq_users_db.users')
        .select('user_id, username, email, role, created_at, updated_at')
        .eq('user_id', payload.user_id)
        .limit(1);

      if (error || !users || users.length === 0) {
        return null;
      }

      return users[0] as User;
    } catch (error) {
      return null;
    }
  }
}
