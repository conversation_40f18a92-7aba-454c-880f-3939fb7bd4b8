{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\n\n// Environment variables (only accessed on server-side)\nconst getJWTSecret = () => {\n  const secret = process.env.JWT_SECRET;\n  if (!secret) {\n    throw new Error('JWT_SECRET environment variable is required');\n  }\n  return secret;\n};\n\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\n// Types\nexport interface User {\n  user_id: string;\n  username: string;\n  email: string;\n  role: 'user' | 'admin';\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface JWTPayload {\n  user_id: string;\n  role: 'user' | 'admin';\n  iat?: number;\n  exp?: number;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  token?: string;\n  error?: string;\n}\n\n// Password hashing utilities\nexport class PasswordUtils {\n  private static readonly SALT_ROUNDS = 12; // High security salt rounds\n\n  /**\n   * Hash a password using bcrypt with salt\n   * @param password - Plain text password\n   * @returns Promise<string> - Hashed password\n   */\n  static async hashPassword(password: string): Promise<string> {\n    try {\n      const salt = await bcrypt.genSalt(this.SALT_ROUNDS);\n      const hash = await bcrypt.hash(password, salt);\n      return hash;\n    } catch (error) {\n      throw new Error('Failed to hash password');\n    }\n  }\n\n  /**\n   * Verify a password against its hash\n   * @param password - Plain text password\n   * @param hash - Stored password hash\n   * @returns Promise<boolean> - True if password matches\n   */\n  static async verifyPassword(password: string, hash: string): Promise<boolean> {\n    try {\n      return await bcrypt.compare(password, hash);\n    } catch (error) {\n      throw new Error('Failed to verify password');\n    }\n  }\n}\n\n// JWT utilities\nexport class JWTUtils {\n  /**\n   * Generate a JWT token for a user\n   * @param payload - JWT payload containing user_id and role\n   * @returns string - Signed JWT token\n   */\n  static generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {\n    try {\n      return jwt.sign(payload, getJWTSecret(), {\n        expiresIn: JWT_EXPIRES_IN,\n        algorithm: 'HS256'\n      });\n    } catch (error) {\n      throw new Error('Failed to generate JWT token');\n    }\n  }\n\n  /**\n   * Verify and decode a JWT token\n   * @param token - JWT token to verify\n   * @returns JWTPayload - Decoded payload\n   */\n  static verifyToken(token: string): JWTPayload {\n    try {\n      const decoded = jwt.verify(token, getJWTSecret(), {\n        algorithms: ['HS256']\n      }) as JWTPayload;\n      return decoded;\n    } catch (error) {\n      if (error instanceof jwt.TokenExpiredError) {\n        throw new Error('Token has expired');\n      } else if (error instanceof jwt.JsonWebTokenError) {\n        throw new Error('Invalid token');\n      } else {\n        throw new Error('Token verification failed');\n      }\n    }\n  }\n\n  /**\n   * Extract token from Authorization header\n   * @param authHeader - Authorization header value\n   * @returns string | null - Extracted token or null\n   */\n  static extractTokenFromHeader(authHeader: string | null): string | null {\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return null;\n    }\n    return authHeader.substring(7); // Remove 'Bearer ' prefix\n  }\n}\n\n// Cookie configuration constants\nexport const COOKIE_CONFIG = {\n  NAME: 'haq_auth_token',\n  OPTIONS: {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'strict' as const,\n    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds\n    path: '/'\n  }\n};\n\n// Input validation utilities\nexport class ValidationUtils {\n  /**\n   * Validate email format\n   * @param email - Email to validate\n   * @returns boolean - True if valid\n   */\n  static isValidEmail(email: string): boolean {\n    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n    return emailRegex.test(email) && email.length <= 255;\n  }\n\n  /**\n   * Validate username format\n   * @param username - Username to validate\n   * @returns boolean - True if valid\n   */\n  static isValidUsername(username: string): boolean {\n    // Username: 3-50 characters, alphanumeric and underscores only\n    const usernameRegex = /^[a-zA-Z0-9_]{3,50}$/;\n    return usernameRegex.test(username);\n  }\n\n  /**\n   * Validate password strength\n   * @param password - Password to validate\n   * @returns { isValid: boolean, errors: string[] }\n   */\n  static validatePassword(password: string): { isValid: boolean; errors: string[] } {\n    const errors: string[] = [];\n\n    if (password.length < 8) {\n      errors.push('Password must be at least 8 characters long');\n    }\n\n    if (password.length > 128) {\n      errors.push('Password must be less than 128 characters');\n    }\n\n    if (!/[a-z]/.test(password)) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n\n    if (!/[A-Z]/.test(password)) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n\n    if (!/[0-9]/.test(password)) {\n      errors.push('Password must contain at least one number');\n    }\n\n    if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n      errors.push('Password must contain at least one special character');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Sanitize input string to prevent XSS\n   * @param input - Input string to sanitize\n   * @returns string - Sanitized string\n   */\n  static sanitizeInput(input: string): string {\n    return input\n      .trim()\n      .replace(/[<>]/g, '') // Remove potential HTML tags\n      .substring(0, 1000); // Limit length\n  }\n}\n\n// Authentication helper functions (client-side)\nexport class AuthHelpers {\n  /**\n   * Verify JWT token and extract payload\n   * @param token - JWT token to verify\n   * @returns JWTPayload | null - Decoded payload or null if invalid\n   */\n  static verifyTokenSafe(token: string): JWTPayload | null {\n    try {\n      return JWTUtils.verifyToken(token);\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Check if user has admin role from token\n   * @param token - JWT token\n   * @returns boolean - True if user is admin\n   */\n  static isAdminFromToken(token: string): boolean {\n    try {\n      const payload = JWTUtils.verifyToken(token);\n      return payload.role === 'admin';\n    } catch (error) {\n      return false;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEA,uDAAuD;AACvD,MAAM,eAAe;IACnB,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;IACrC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AA2B9C,MAAM;IACX,OAAwB,cAAc,GAAG;IAEzC;;;;GAIC,GACD,aAAa,aAAa,QAAgB,EAAmB;QAC3D,IAAI;YACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;YAClD,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;;GAKC,GACD,aAAa,eAAe,QAAgB,EAAE,IAAY,EAAoB;QAC5E,IAAI;YACF,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;QACxC,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,cAAc,OAAwC,EAAU;QACrE,IAAI;YACF,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,gBAAgB;gBACvC,WAAW;gBACX,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;;;GAIC,GACD,OAAO,YAAY,KAAa,EAAc;QAC5C,IAAI;YACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,gBAAgB;gBAChD,YAAY;oBAAC;iBAAQ;YACvB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBAC1C,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBACjD,MAAM,IAAI,MAAM;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA;;;;GAIC,GACD,OAAO,uBAAuB,UAAyB,EAAiB;QACtE,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO;QACT;QACA,OAAO,WAAW,SAAS,CAAC,IAAI,0BAA0B;IAC5D;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;IACN,SAAS;QACP,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,IAAI,KAAK,KAAK;QACtB,MAAM;IACR;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,aAAa,KAAa,EAAW;QAC1C,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,MAAM,IAAI;IACnD;IAEA;;;;GAIC,GACD,OAAO,gBAAgB,QAAgB,EAAW;QAChD,+DAA+D;QAC/D,MAAM,gBAAgB;QACtB,OAAO,cAAc,IAAI,CAAC;IAC5B;IAEA;;;;GAIC,GACD,OAAO,iBAAiB,QAAgB,EAA0C;QAChF,MAAM,SAAmB,EAAE;QAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,SAAS,MAAM,GAAG,KAAK;YACzB,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;YAC3B,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,wCAAwC,IAAI,CAAC,WAAW;YAC3D,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;IAEA;;;;GAIC,GACD,OAAO,cAAc,KAAa,EAAU;QAC1C,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,6BAA6B;SAClD,SAAS,CAAC,GAAG,OAAO,eAAe;IACxC;AACF;AAGO,MAAM;IACX;;;;GAIC,GACD,OAAO,gBAAgB,KAAa,EAAqB;QACvD,IAAI;YACF,OAAO,SAAS,WAAW,CAAC;QAC9B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,OAAO,iBAAiB,KAAa,EAAW;QAC9C,IAAI;YACF,MAAM,UAAU,SAAS,WAAW,CAAC;YACrC,OAAO,QAAQ,IAAI,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/auth-server.ts"], "sourcesContent": ["import { cookies } from 'next/headers';\nimport { JW<PERSON>tils, COOKIE_CONFIG, type User, type JWTPayload } from './auth';\n\n// Direct database query function using service role\nasync function queryDatabase(sql: string, params: any[] = []): Promise<any> {\n  try {\n    const response = await fetch(`https://wqbuilazpyxpwyuwuqpi.supabase.co/rest/v1/rpc/exec_sql`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,\n        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!\n      },\n      body: JSON.stringify({ sql, args: params })\n    });\n\n    if (!response.ok) {\n      throw new Error(`Database query failed: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Database query error:', error);\n    throw error;\n  }\n}\n\n// Server-side cookie management utilities\nexport class ServerCookieUtils {\n  /**\n   * Set authentication cookie (server-side)\n   * @param token - JWT token to store\n   */\n  static async setAuthCookie(token: string): Promise<void> {\n    const cookieStore = await cookies();\n    cookieStore.set(COOKIE_CONFIG.NAME, token, COOKIE_CONFIG.OPTIONS);\n  }\n\n  /**\n   * Get authentication token from cookie (server-side)\n   * @returns Promise<string | null> - Token or null if not found\n   */\n  static async getAuthToken(): Promise<string | null> {\n    try {\n      const cookieStore = await cookies();\n      const cookie = cookieStore.get(COOKIE_CONFIG.NAME);\n      return cookie?.value || null;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Remove authentication cookie (server-side)\n   */\n  static async removeAuthCookie(): Promise<void> {\n    const cookieStore = await cookies();\n    cookieStore.delete(COOKIE_CONFIG.NAME);\n  }\n}\n\n// Server-side authentication helper functions\nexport class ServerAuthHelpers {\n  /**\n   * Get current user from request (server-side)\n   * @returns Promise<User | null> - Current user or null\n   */\n  static async getCurrentUser(): Promise<User | null> {\n    try {\n      const token = await ServerCookieUtils.getAuthToken();\n      if (!token) {\n        return null;\n      }\n\n      const payload = JWTUtils.verifyToken(token);\n      \n      // Fetch full user data using direct SQL query\n      const result = await queryDatabase(\n        'SELECT user_id, username, email, role, created_at, updated_at FROM haq_users_db.users WHERE user_id = $1 LIMIT 1',\n        [payload.user_id]\n      );\n\n      const users = result || [];\n      const error = null;\n\n      if (error || !users || users.length === 0) {\n        return null;\n      }\n\n      return users[0] as User;\n    } catch (error) {\n      return null;\n    }\n  }\n\n  /**\n   * Check if user is authenticated (server-side)\n   * @returns Promise<boolean> - True if authenticated\n   */\n  static async isAuthenticated(): Promise<boolean> {\n    const user = await this.getCurrentUser();\n    return user !== null;\n  }\n\n  /**\n   * Check if user has admin role (server-side)\n   * @returns Promise<boolean> - True if user is admin\n   */\n  static async isAdmin(): Promise<boolean> {\n    try {\n      const token = await ServerCookieUtils.getAuthToken();\n      if (!token) {\n        return false;\n      }\n\n      const payload = JWTUtils.verifyToken(token);\n      return payload.role === 'admin';\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Verify token and get user data (server-side)\n   * @param token - JWT token to verify\n   * @returns Promise<User | null> - User data or null if invalid\n   */\n  static async verifyTokenAndGetUser(token: string): Promise<User | null> {\n    try {\n      const payload = JWTUtils.verifyToken(token);\n      \n      // Fetch user data from database\n      const users = await queryDatabase(\n        'SELECT user_id, username, email, role, created_at, updated_at FROM haq_users_db.users WHERE user_id = $1 LIMIT 1',\n        [payload.user_id]\n      );\n      const error = null;\n\n      if (error || !users || users.length === 0) {\n        return null;\n      }\n\n      return users[0] as User;\n    } catch (error) {\n      return null;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,oDAAoD;AACpD,eAAe,cAAc,GAAW,EAAE,SAAgB,EAAE;IAC1D,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,6DAA6D,CAAC,EAAE;YAC5F,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,yBAAyB,EAAE;gBAClE,UAAU,QAAQ,GAAG,CAAC,yBAAyB;YACjD;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAK,MAAM;YAAO;QAC3C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,EAAE;QAC7D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM;IACX;;;GAGC,GACD,aAAa,cAAc,KAAa,EAAiB;QACvD,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,YAAY,GAAG,CAAC,oHAAA,CAAA,gBAAa,CAAC,IAAI,EAAE,OAAO,oHAAA,CAAA,gBAAa,CAAC,OAAO;IAClE;IAEA;;;GAGC,GACD,aAAa,eAAuC;QAClD,IAAI;YACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;YAChC,MAAM,SAAS,YAAY,GAAG,CAAC,oHAAA,CAAA,gBAAa,CAAC,IAAI;YACjD,OAAO,QAAQ,SAAS;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAa,mBAAkC;QAC7C,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,YAAY,MAAM,CAAC,oHAAA,CAAA,gBAAa,CAAC,IAAI;IACvC;AACF;AAGO,MAAM;IACX;;;GAGC,GACD,aAAa,iBAAuC;QAClD,IAAI;YACF,MAAM,QAAQ,MAAM,kBAAkB,YAAY;YAClD,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,MAAM,UAAU,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAErC,8CAA8C;YAC9C,MAAM,SAAS,MAAM,cACnB,oHACA;gBAAC,QAAQ,OAAO;aAAC;YAGnB,MAAM,QAAQ,UAAU,EAAE;YAC1B,MAAM,QAAQ;YAEd,IAAI,SAAS,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBACzC,OAAO;YACT;YAEA,OAAO,KAAK,CAAC,EAAE;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;GAGC,GACD,aAAa,kBAAoC;QAC/C,MAAM,OAAO,MAAM,IAAI,CAAC,cAAc;QACtC,OAAO,SAAS;IAClB;IAEA;;;GAGC,GACD,aAAa,UAA4B;QACvC,IAAI;YACF,MAAM,QAAQ,MAAM,kBAAkB,YAAY;YAClD,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,MAAM,UAAU,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YACrC,OAAO,QAAQ,IAAI,KAAK;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,aAAa,sBAAsB,KAAa,EAAwB;QACtE,IAAI;YACF,MAAM,UAAU,oHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC;YAErC,gCAAgC;YAChC,MAAM,QAAQ,MAAM,cAClB,oHACA;gBAAC,QAAQ,OAAO;aAAC;YAEnB,MAAM,QAAQ;YAEd,IAAI,SAAS,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;gBACzC,OAAO;YACT;YAEA,OAAO,KAAK,CAAC,EAAE;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { PasswordUtils, ValidationUtils, JWTUtils } from '@/lib/auth';\nimport { ServerCookieUtils } from '@/lib/auth-server';\n\n// Direct database query function using service role\nasync function queryDatabase(sql: string, params: any[] = []): Promise<any> {\n  try {\n    const response = await fetch(`https://wqbuilazpyxpwyuwuqpi.supabase.co/rest/v1/rpc/exec_sql`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,\n        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!\n      },\n      body: JSON.stringify({ sql, args: params })\n    });\n\n    if (!response.ok) {\n      throw new Error(`Database query failed: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Database query error:', error);\n    throw error;\n  }\n}\n\ninterface LoginRequest {\n  email: string;\n  password: string;\n}\n\ninterface LoginResponse {\n  success: boolean;\n  message: string;\n  user?: {\n    user_id: string;\n    username: string;\n    email: string;\n    role: string;\n  };\n}\n\nexport async function POST(request: NextRequest): Promise<NextResponse<LoginResponse>> {\n  try {\n    // Parse request body\n    const body: LoginRequest = await request.json();\n    const { email, password } = body;\n\n    // Input validation\n    if (!email || !password) {\n      return NextResponse.json(\n        { success: false, message: 'Email and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Sanitize inputs\n    const sanitizedEmail = ValidationUtils.sanitizeInput(email);\n\n    // Validate email format\n    if (!ValidationUtils.isValidEmail(sanitizedEmail)) {\n      return NextResponse.json(\n        { success: false, message: 'Invalid email format' },\n        { status: 400 }\n      );\n    }\n\n    // Rate limiting check (basic implementation)\n    // In production, you'd want to use a proper rate limiting solution\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n    const clientIP = request.headers.get('x-forwarded-for') || \n                     request.headers.get('x-real-ip') || \n                     'unknown';\n\n    // Find user by email\n    const users = await queryDatabase(\n      'SELECT user_id, username, email, password_hash, role, created_at, updated_at FROM haq_users_db.users WHERE email = $1 LIMIT 1',\n      [sanitizedEmail]\n    );\n    const fetchError = null;\n\n    if (fetchError) {\n      console.error('Database error fetching user:', fetchError);\n      return NextResponse.json(\n        { success: false, message: 'Internal server error' },\n        { status: 500 }\n      );\n    }\n\n    // Check if user exists\n    if (!users || users.length === 0) {\n      // Use generic message to prevent email enumeration\n      return NextResponse.json(\n        { success: false, message: 'Invalid email or password' },\n        { status: 401 }\n      );\n    }\n\n    const user = users[0];\n\n    // Verify password\n    const isPasswordValid = await PasswordUtils.verifyPassword(password, user.password_hash);\n\n    if (!isPasswordValid) {\n      // Log failed login attempt (in production, implement proper logging)\n      console.warn(`Failed login attempt for email: ${sanitizedEmail} from IP: ${clientIP}`);\n      \n      return NextResponse.json(\n        { success: false, message: 'Invalid email or password' },\n        { status: 401 }\n      );\n    }\n\n    // Update last login timestamp (optional)\n    const { error: updateError } = await supabaseUsers\n      .from('users')\n      .update({ updated_at: new Date().toISOString() })\n      .eq('user_id', user.user_id);\n\n    if (updateError) {\n      console.warn('Failed to update last login timestamp:', updateError);\n      // Don't fail the login for this non-critical error\n    }\n\n    // Generate JWT token\n    const token = JWTUtils.generateToken({\n      user_id: user.user_id,\n      role: user.role as 'user' | 'admin'\n    });\n\n    // Set authentication cookie\n    await ServerCookieUtils.setAuthCookie(token);\n\n    // Log successful login (in production, implement proper logging)\n    console.info(`Successful login for user: ${user.username} (${user.user_id}) from IP: ${clientIP}`);\n\n    // Return success response (exclude sensitive data)\n    return NextResponse.json(\n      {\n        success: true,\n        message: 'Login successful',\n        user: {\n          user_id: user.user_id,\n          username: user.username,\n          email: user.email,\n          role: user.role\n        }\n      },\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Login error:', error);\n    \n    // Handle JSON parsing errors\n    if (error instanceof SyntaxError) {\n      return NextResponse.json(\n        { success: false, message: 'Invalid JSON in request body' },\n        { status: 400 }\n      );\n    }\n\n    // Handle password verification errors\n    if (error instanceof Error && error.message.includes('password')) {\n      return NextResponse.json(\n        { success: false, message: 'Authentication failed' },\n        { status: 401 }\n      );\n    }\n\n    // Handle JWT generation errors\n    if (error instanceof Error && error.message.includes('JWT')) {\n      return NextResponse.json(\n        { success: false, message: 'Authentication setup failed' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// Handle unsupported methods\nexport async function GET() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n\nexport async function PUT() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n\nexport async function DELETE() {\n  return NextResponse.json(\n    { success: false, message: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,oDAAoD;AACpD,eAAe,cAAc,GAAW,EAAE,SAAgB,EAAE;IAC1D,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,6DAA6D,CAAC,EAAE;YAC5F,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,yBAAyB,EAAE;gBAClE,UAAU,QAAQ,GAAG,CAAC,yBAAyB;YACjD;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAK,MAAM;YAAO;QAC3C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,EAAE;QAC7D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAkBO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,qBAAqB;QACrB,MAAM,OAAqB,MAAM,QAAQ,IAAI;QAC7C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAE5B,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAkC,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,iBAAiB,oHAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;QAErD,wBAAwB;QACxB,IAAI,CAAC,oHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC,iBAAiB;YACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAuB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,6CAA6C;QAC7C,mEAAmE;QACnE,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QACvD,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;QAEjB,qBAAqB;QACrB,MAAM,QAAQ,MAAM,cAClB,iIACA;YAAC;SAAe;QAElB,MAAM,aAAa;QAEnB,uCAAgB;;QAMhB;QAEA,uBAAuB;QACvB,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,mDAAmD;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA4B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,kBAAkB;QAClB,MAAM,kBAAkB,MAAM,oHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC,UAAU,KAAK,aAAa;QAEvF,IAAI,CAAC,iBAAiB;YACpB,qEAAqE;YACrE,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,eAAe,UAAU,EAAE,UAAU;YAErF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA4B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yCAAyC;QACzC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAClC,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,YAAY,IAAI,OAAO,WAAW;QAAG,GAC9C,EAAE,CAAC,WAAW,KAAK,OAAO;QAE7B,IAAI,aAAa;YACf,QAAQ,IAAI,CAAC,0CAA0C;QACvD,mDAAmD;QACrD;QAEA,qBAAqB;QACrB,MAAM,QAAQ,oHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;YACnC,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI;QACjB;QAEA,4BAA4B;QAC5B,MAAM,8HAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;QAEtC,iEAAiE;QACjE,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,KAAK,QAAQ,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,UAAU;QAEjG,mDAAmD;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,SAAS,KAAK,OAAO;gBACrB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;YACjB;QACF,GACA;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAE9B,6BAA6B;QAC7B,IAAI,iBAAiB,aAAa;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA+B,GAC1D;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,aAAa;YAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAwB,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;YAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAA8B,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;IAAI;AAElB;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;IAAI;AAElB;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,SAAS;QAAO,SAAS;IAAqB,GAChD;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}