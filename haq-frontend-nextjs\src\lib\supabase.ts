import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Singleton pattern to prevent multiple client instances
let supabaseInstance: ReturnType<typeof createBrowserClient> | null = null

// Client-side Supabase client
export const createClientComponentClient = () => {
  if (!supabaseInstance) {
    supabaseInstance = createBrowserClient(supabaseUrl, supabaseAnonKey)
  }
  return supabaseInstance
}

// Legacy client for backward compatibility
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Create separate clients for different schemas
export const supabaseUsers = createClient(supabaseUrl, supabaseAnonKey, {
  db: { schema: 'haq_users_db' }
})

export const supabaseContent = createClient(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>, {
  db: { schema: 'haq_content_db' }
})

// Database types (will be generated later)
export type Database = {
  haq_users_db: {
    Tables: {
      users: {
        Row: {
          user_id: string
          username: string
          email: string
          password_hash: string
          role: 'user' | 'admin'
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id?: string
          username: string
          email: string
          password_hash: string
          role?: 'user' | 'admin'
          created_at?: string
          updated_at?: string
        }
        Update: {
          user_id?: string
          username?: string
          email?: string
          password_hash?: string
          role?: 'user' | 'admin'
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
  haq_content_db: {
    Tables: {
      companies: {
        Row: {
          company_id: string
          name: string
          slug: string
          industry: string | null
          location: string | null
          description: string | null
          website_url: string | null
          logo_url: string | null
          employee_count_range: string | null
          founded_year: number | null
          haq_score: number
          total_reviews: number
          is_verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          company_id?: string
          name: string
          slug: string
          industry?: string | null
          location?: string | null
          description?: string | null
          website_url?: string | null
          logo_url?: string | null
          employee_count_range?: string | null
          founded_year?: number | null
          haq_score?: number
          total_reviews?: number
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          company_id?: string
          name?: string
          slug?: string
          industry?: string | null
          location?: string | null
          description?: string | null
          website_url?: string | null
          logo_url?: string | null
          employee_count_range?: string | null
          founded_year?: number | null
          haq_score?: number
          total_reviews?: number
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      reviews: {
        Row: {
          review_id: string
          company_id: string
          anonymous_user_hash: string
          overall_rating: number | null
          work_life_balance_rating: number | null
          compensation_rating: number | null
          management_rating: number | null
          culture_rating: number | null
          title: string
          pros: string | null
          cons: string | null
          advice_to_management: string | null
          job_title: string | null
          employment_status: 'current' | 'former' | null
          employment_duration: string | null
          department: string | null
          location: string | null
          is_approved: boolean
          is_featured: boolean
          helpful_count: number
          created_at: string
          updated_at: string
        }
      }
      salary_reports: {
        Row: {
          salary_id: string
          company_id: string
          anonymous_user_hash: string
          job_title: string
          department: string | null
          location: string | null
          experience_level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive' | null
          base_salary: number | null
          bonus: number
          stock_options: number
          total_compensation: number | null
          currency: string
          employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | null
          years_of_experience: number | null
          years_at_company: number | null
          is_approved: boolean
          created_at: string
          updated_at: string
        }
      }
      company_flags: {
        Row: {
          flag_id: string
          company_id: string
          flag_type: 'red' | 'green' | null
          flag_text: string
          flag_count: number
          created_at: string
        }
      }
    }
  }
}
