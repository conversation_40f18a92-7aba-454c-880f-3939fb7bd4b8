import { NextRequest, NextResponse } from 'next/server';
import { JWTUtils } from '@/lib/auth';
import { ServerCookieUtils } from '@/lib/auth-server';
import { supabase } from '@/lib/supabase';

interface UserProfileResponse {
  success: boolean;
  message: string;
  user?: {
    user_id: string;
    username: string;
    email: string;
    role: string;
    created_at: string;
  };
}

export async function GET(request: NextRequest): Promise<NextResponse<UserProfileResponse>> {
  try {
    // Get authentication token from cookie
    const token = await ServerCookieUtils.getAuthToken();

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify and decode JWT token
    let payload;
    try {
      payload = JWTUtils.verifyToken(token);
    } catch (error) {
      // Token is invalid or expired
      await ServerCookieUtils.removeAuthCookie(); // Clear invalid cookie
      return NextResponse.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Fetch user data from database
    const { data: users, error: fetchError } = await supabase
      .from('haq_users_db.users')
      .select('user_id, username, email, role, created_at')
      .eq('user_id', payload.user_id)
      .limit(1);

    if (fetchError) {
      console.error('Database error fetching user profile:', fetchError);
      return NextResponse.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      );
    }

    // Check if user exists
    if (!users || users.length === 0) {
      // User doesn't exist in database but has valid token
      // This could happen if user was deleted
      await ServerCookieUtils.removeAuthCookie();
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    const user = users[0];

    // Return user profile (exclude sensitive data)
    return NextResponse.json(
      {
        success: true,
        message: 'User profile retrieved successfully',
        user: {
          user_id: user.user_id,
          username: user.username,
          email: user.email,
          role: user.role,
          created_at: user.created_at
        }
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Get user profile error:', error);

    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
