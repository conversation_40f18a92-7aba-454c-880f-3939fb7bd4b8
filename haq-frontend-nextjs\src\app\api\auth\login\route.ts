import { NextRequest, NextResponse } from 'next/server';
import { PasswordUtils, ValidationUtils, JWTUtils } from '@/lib/auth';
import { ServerCookieUtils } from '@/lib/auth-server';
import { supabaseUsers } from '@/lib/supabase';

interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    user_id: string;
    username: string;
    email: string;
    role: string;
  };
}

export async function POST(request: NextRequest): Promise<NextResponse<LoginResponse>> {
  try {
    // Parse request body
    const body: LoginRequest = await request.json();
    const { email, password } = body;

    // Input validation
    if (!email || !password) {
      return NextResponse.json(
        { success: false, message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Sanitize inputs
    const sanitizedEmail = ValidationUtils.sanitizeInput(email);

    // Validate email format
    if (!ValidationUtils.isValidEmail(sanitizedEmail)) {
      return NextResponse.json(
        { success: false, message: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Rate limiting check (basic implementation)
    // In production, you'd want to use a proper rate limiting solution
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    // Find user by email
    const { data: users, error: fetchError } = await supabaseUsers
      .from('users')
      .select('user_id, username, email, password_hash, role, created_at, updated_at')
      .eq('email', sanitizedEmail)
      .limit(1);

    if (fetchError) {
      console.error('Database error fetching user:', fetchError);
      return NextResponse.json(
        { success: false, message: 'Internal server error' },
        { status: 500 }
      );
    }

    // Check if user exists
    if (!users || users.length === 0) {
      // Use generic message to prevent email enumeration
      return NextResponse.json(
        { success: false, message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    const user = users[0];

    // Verify password
    const isPasswordValid = await PasswordUtils.verifyPassword(password, user.password_hash);

    if (!isPasswordValid) {
      // Log failed login attempt (in production, implement proper logging)
      console.warn(`Failed login attempt for email: ${sanitizedEmail} from IP: ${clientIP}`);
      
      return NextResponse.json(
        { success: false, message: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Update last login timestamp (optional)
    const { error: updateError } = await supabaseUsers
      .from('users')
      .update({ updated_at: new Date().toISOString() })
      .eq('user_id', user.user_id);

    if (updateError) {
      console.warn('Failed to update last login timestamp:', updateError);
      // Don't fail the login for this non-critical error
    }

    // Generate JWT token
    const token = JWTUtils.generateToken({
      user_id: user.user_id,
      role: user.role as 'user' | 'admin'
    });

    // Set authentication cookie
    await ServerCookieUtils.setAuthCookie(token);

    // Log successful login (in production, implement proper logging)
    console.info(`Successful login for user: ${user.username} (${user.user_id}) from IP: ${clientIP}`);

    // Return success response (exclude sensitive data)
    return NextResponse.json(
      {
        success: true,
        message: 'Login successful',
        user: {
          user_id: user.user_id,
          username: user.username,
          email: user.email,
          role: user.role
        }
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Login error:', error);
    
    // Handle JSON parsing errors
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { success: false, message: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Handle password verification errors
    if (error instanceof Error && error.message.includes('password')) {
      return NextResponse.json(
        { success: false, message: 'Authentication failed' },
        { status: 401 }
      );
    }

    // Handle JWT generation errors
    if (error instanceof Error && error.message.includes('JWT')) {
      return NextResponse.json(
        { success: false, message: 'Authentication setup failed' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
