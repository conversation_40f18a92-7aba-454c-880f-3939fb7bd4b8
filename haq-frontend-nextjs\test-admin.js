// Simple test script to verify admin functionality
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3000';

async function testAdminLogin() {
  console.log('Testing admin login...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    const result = await response.json();
    console.log('Login response:', result);

    if (result.success) {
      // Extract cookies from response
      const cookies = response.headers.get('set-cookie');
      console.log('Login successful, cookies:', cookies);
      return cookies;
    } else {
      console.log('Login failed:', result.message);
      return null;
    }
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
}

async function testAdminCompaniesAPI(cookies) {
  console.log('\nTesting admin companies API...');
  
  if (!cookies) {
    console.log('No cookies available, skipping API test');
    return;
  }

  try {
    // Test GET companies
    console.log('Testing GET /api/admin/companies...');
    const getResponse = await fetch(`${BASE_URL}/api/admin/companies`, {
      method: 'GET',
      headers: {
        'Cookie': cookies
      }
    });

    const getResult = await getResponse.json();
    console.log('GET companies response:', getResult);

    // Test POST company
    console.log('\nTesting POST /api/admin/companies...');
    const postResponse = await fetch(`${BASE_URL}/api/admin/companies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      },
      body: JSON.stringify({
        name: 'Test Company Ltd',
        industry: 'Technology',
        hq_location: 'Karachi, Pakistan',
        description: 'A test company for admin functionality verification',
        website_url: 'https://testcompany.com',
        employee_count_range: '51-200',
        founded_year: 2020
      })
    });

    const postResult = await postResponse.json();
    console.log('POST company response:', postResult);

  } catch (error) {
    console.error('API test error:', error);
  }
}

async function runTests() {
  console.log('Starting admin functionality tests...\n');
  
  const cookies = await testAdminLogin();
  await testAdminCompaniesAPI(cookies);
  
  console.log('\nTests completed!');
}

runTests();
